import pygame
import random

pygame.init()

# Display settings
display_width = 800
display_height = 600
screen = pygame.display.set_mode((display_width, display_height))
pygame.display.set_caption("Rock Paper Scissors")

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
BLUE = (0, 100, 200)
GREEN = (0, 200, 0)
RED = (200, 0, 0)

# Game variables
player_choice = None
computer_choice = None
result = ""

# Button class
class Button:
    def __init__(self, x, y, width, height, text):
        self.rect = pygame.Rect(x, y, width, height)
        self.text = text
        self.font = pygame.font.Font(None, 36)

    def draw(self, screen):
        pygame.draw.rect(screen, BLUE, self.rect)
        pygame.draw.rect(screen, WHITE, self.rect, 2)

        text_surface = self.font.render(self.text, True, WHITE)
        text_rect = text_surface.get_rect(center=self.rect.center)
        screen.blit(text_surface, text_rect)

    def is_clicked(self, mouse_pos):
        return self.rect.collidepoint(mouse_pos)

# Create buttons
rock_button = Button(150, 400, 120, 60, "Rock")
paper_button = Button(340, 400, 120, 60, "Paper")
scissors_button = Button(530, 400, 120, 60, "Scissors")

def computer_random_choice():
    """Choose one of 3 options evenly for computer"""
    choices = ["Rock", "Paper", "Scissors"]
    return random.choice(choices)

def determine_winner(player, computer):
    """Determine who wins the game"""
    if player == computer:
        return "It's a tie!"
    elif (player == "Rock" and computer == "Scissors") or \
         (player == "Paper" and computer == "Rock") or \
         (player == "Scissors" and computer == "Paper"):
        return "You win!"
    else:
        return "Computer wins!"

# Game loop
clock = pygame.time.Clock()
running = True

print("Welcome to Rock Paper Scissors!")

while running:
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left mouse button
                mouse_pos = pygame.mouse.get_pos()

                if rock_button.is_clicked(mouse_pos):
                    player_choice = "Rock"
                    computer_choice = computer_random_choice()
                    result = determine_winner(player_choice, computer_choice)

                elif paper_button.is_clicked(mouse_pos):
                    player_choice = "Paper"
                    computer_choice = computer_random_choice()
                    result = determine_winner(player_choice, computer_choice)

                elif scissors_button.is_clicked(mouse_pos):
                    player_choice = "Scissors"
                    computer_choice = computer_random_choice()
                    result = determine_winner(player_choice, computer_choice)

    # Draw everything
    screen.fill(BLACK)

    # Draw title
    font = pygame.font.Font(None, 48)
    title = font.render("Rock Paper Scissors", True, WHITE)
    title_rect = title.get_rect(center=(display_width//2, 50))
    screen.blit(title, title_rect)

    # Draw game buttons only when no game is in progress
    if not player_choice:
        rock_button.draw(screen)
        paper_button.draw(screen)
        scissors_button.draw(screen)

    # Draw choices and result if game has been played
    if player_choice and computer_choice:
        font = pygame.font.Font(None, 36)

        player_text = font.render(f"You chose: {player_choice}", True, WHITE)
        computer_text = font.render(f"Computer chose: {computer_choice}", True, WHITE)
        result_text = font.render(result, True, WHITE)

        screen.blit(player_text, (50, 200))
        screen.blit(computer_text, (50, 250))
        screen.blit(result_text, (50, 300))

        # Show play again button after game is finished
        play_again_button.draw(screen)

    # Instructions
    font = pygame.font.Font(None, 24)
    if not player_choice:
        instruction = font.render("Click a button to make your choice!", True, WHITE)
        screen.blit(instruction, (50, 500))
    else:
        instruction = font.render("Click 'Play Again?' to start a new round!", True, WHITE)
        screen.blit(instruction, (50, 500))

    pygame.display.flip()
    clock.tick(60)

pygame.quit()