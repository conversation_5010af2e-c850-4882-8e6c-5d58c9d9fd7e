def fibonacci_sequence(n):
    """
    Generate the first n numbers in the Fibonacci sequence
    """
    if n <= 0:
        return []
    elif n == 1:
        return [0]
    elif n == 2:
        return [0, 1]
    
    fib_sequence = [0, 1]
    for i in range(2, n):
        next_fib = fib_sequence[i-1] + fib_sequence[i-2]
        fib_sequence.append(next_fib)
    
    return fib_sequence

def fibonacci_nth(n):
    """
    Calculate the nth Fibonacci number (more memory efficient)
    """
    if n <= 0:
        return 0
    elif n == 1:
        return 1
    
    a, b = 0, 1
    for i in range(2, n + 1):
        a, b = b, a + b
    
    return b

def count_digits(number):
    """
    Count the number of digits in a number
    """
    return len(str(abs(number)))

def fibonacci_with_digit_count(n):
    """
    Generate Fibonacci sequence with digit count for each number
    """
    results = []
    
    if n <= 0:
        return results
    
    a, b = 0, 1
    
    # First number (0)
    if n >= 1:
        results.append({
            'position': 1,
            'fibonacci': a,
            'digits': count_digits(a) if a != 0 else 1
        })
    
    # Second number (1)
    if n >= 2:
        results.append({
            'position': 2,
            'fibonacci': b,
            'digits': count_digits(b)
        })
    
    # Generate remaining numbers
    for i in range(3, n + 1):
        a, b = b, a + b
        results.append({
            'position': i,
            'fibonacci': b,
            'digits': count_digits(b)
        })
    
    return results

def find_fibonacci_with_n_digits(target_digits):
    """
    Find the first Fibonacci number with exactly n digits
    """
    if target_digits <= 0:
        return None
    
    position = 1
    a, b = 0, 1
    
    # Check first number (0)
    if target_digits == 1 and a == 0:
        return {'position': 1, 'fibonacci': a, 'digits': 1}
    
    # Check second number (1)
    if target_digits == 1:
        return {'position': 2, 'fibonacci': b, 'digits': 1}
    
    position = 2
    while True:
        position += 1
        a, b = b, a + b
        digits = count_digits(b)
        
        if digits == target_digits:
            return {'position': position, 'fibonacci': b, 'digits': digits}
        elif digits > target_digits:
            return None  # We've passed the target

def main():
    print("=== Fibonacci Sequence Digit Calculator ===")
    print("Calculate Fibonacci numbers and analyze their digits!")
    
    while True:
        print("\nChoose an option:")
        print("1. Generate Fibonacci sequence with digit counts")
        print("2. Calculate specific Fibonacci number")
        print("3. Find first Fibonacci number with N digits")
        print("4. Fibonacci digit analysis")
        print("5. Exit")
        
        choice = input("\nEnter your choice (1-5): ").strip()
        
        if choice == "1":
            try:
                n = int(input("How many Fibonacci numbers to generate? "))
                if n <= 0:
                    print("Please enter a positive number!")
                    continue
                
                print(f"\nFirst {n} Fibonacci numbers with digit counts:")
                print("-" * 50)
                print(f"{'Position':<10} {'Fibonacci':<20} {'Digits':<10}")
                print("-" * 50)
                
                results = fibonacci_with_digit_count(n)
                for result in results:
                    print(f"{result['position']:<10} {result['fibonacci']:<20} {result['digits']:<10}")
                    
            except ValueError:
                print("Please enter a valid number!")
        
        elif choice == "2":
            try:
                n = int(input("Which Fibonacci number to calculate (position)? "))
                if n <= 0:
                    print("Please enter a positive number!")
                    continue
                
                fib_num = fibonacci_nth(n)
                digits = count_digits(fib_num)
                
                print(f"\nFibonacci number at position {n}:")
                print(f"Value: {fib_num}")
                print(f"Number of digits: {digits}")
                
            except ValueError:
                print("Please enter a valid number!")
        
        elif choice == "3":
            try:
                target_digits = int(input("Find first Fibonacci number with how many digits? "))
                if target_digits <= 0:
                    print("Please enter a positive number!")
                    continue
                
                result = find_fibonacci_with_n_digits(target_digits)
                if result:
                    print(f"\nFirst Fibonacci number with {target_digits} digits:")
                    print(f"Position: {result['position']}")
                    print(f"Value: {result['fibonacci']}")
                    print(f"Digits: {result['digits']}")
                else:
                    print(f"No Fibonacci number found with exactly {target_digits} digits in reasonable range.")
                    
            except ValueError:
                print("Please enter a valid number!")
        
        elif choice == "4":
            try:
                n = int(input("Analyze first how many Fibonacci numbers? "))
                if n <= 0:
                    print("Please enter a positive number!")
                    continue
                
                results = fibonacci_with_digit_count(n)
                
                print(f"\n=== Fibonacci Digit Analysis (First {n} numbers) ===")
                
                # Count numbers by digit length
                digit_counts = {}
                for result in results:
                    digits = result['digits']
                    digit_counts[digits] = digit_counts.get(digits, 0) + 1
                
                print("\nDigit length distribution:")
                for digits in sorted(digit_counts.keys()):
                    count = digit_counts[digits]
                    print(f"{digits} digit(s): {count} numbers")
                
                # Find largest number
                largest = max(results, key=lambda x: x['fibonacci'])
                print(f"\nLargest Fibonacci number:")
                print(f"Position {largest['position']}: {largest['fibonacci']} ({largest['digits']} digits)")
                
            except ValueError:
                print("Please enter a valid number!")
        
        elif choice == "5":
            print("Thanks for using the Fibonacci Calculator!")
            break
        
        else:
            print("Invalid choice! Please try again.")

if __name__ == "__main__":
    main()
