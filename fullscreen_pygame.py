import pygame
import sys
import os
import random

# Initialize pygame
pygame.init()

# Get display info for proper fullscreen resolution
display_info = pygame.display.Info()
monitor_width, monitor_height = display_info.current_w, display_info.current_h

# Set up display
width, height = 800, 600
screen = pygame.display.set_mode((width, height), pygame.RESIZABLE)
pygame.display.set_caption("Fullscreen Toggle Demo")

# Colors
BLACK = (0, 0, 0)
WHITE = (255, 255, 255)
BLUE = (0, 100, 200)
LIGHT_BLUE = (100, 150, 255)
GREEN = (0, 200, 0)
BLUE = (0, 100, 200)
LIGHT_BLUE = (100, 150, 255)
GREEN = (0, 200, 0)

# Fullscreen state and window position tracking
fullscreen = False
windowed_size = (width, height)
window_pos = None

# Clock for consistent framerate
clock = pygame.time.Clock()

# Simple Button class
class Button:
    def __init__(self, x, y, width, height, text):
        self.rect = pygame.Rect(x, y, width, height)
        self.text = text
        self.font = pygame.font.Font(None, 36)
        self.clicked = False

    def draw(self, screen):
        # Change color if clicked
        color = GREEN if self.clicked else BLUE
        pygame.draw.rect(screen, color, self.rect)
        pygame.draw.rect(screen, WHITE, self.rect, 2)  # Border

        # Draw text
        text_surface = self.font.render(self.text, True, WHITE)
        text_rect = text_surface.get_rect(center=self.rect.center)
        screen.blit(text_surface, text_rect)

    def is_clicked(self, mouse_pos):
        return self.rect.collidepoint(mouse_pos)

# Create a button
button = Button(300, 250, 200, 60, "Button")

def toggle_fullscreen():
    """Toggle between fullscreen and windowed mode with proper display restoration"""
    global screen, fullscreen, windowed_size, window_pos

    if not fullscreen:
        # Going to fullscreen
        # Save current window size
        windowed_size = screen.get_size()

        # Try to get window position (this may not work on all systems)
        try:
            window_pos = pygame.display.get_window_pos() if hasattr(pygame.display, 'get_window_pos') else None
        except:
            window_pos = None

        # Use FULLSCREEN_DESKTOP for better compatibility with other apps
        # This maintains the desktop resolution and is less disruptive
        screen = pygame.display.set_mode((0, 0), pygame.FULLSCREEN)
        fullscreen = True

        # Hide mouse cursor in fullscreen
        pygame.mouse.set_visible(False)

    else:
        # Going to windowed mode
        # Show mouse cursor
        pygame.mouse.set_visible(True)

        # Restore windowed mode with saved size
        screen = pygame.display.set_mode(windowed_size, pygame.RESIZABLE)
        fullscreen = False

        # Try to restore window position (may not work on all systems)
        if window_pos and hasattr(pygame.display, 'set_window_pos'):
            try:
                pygame.display.set_window_pos(window_pos)
            except:
                pass  # Ignore if position restoration fails

        # Force a display update to ensure proper restoration
        pygame.display.flip()

        # Small delay to allow system to properly restore display
        pygame.time.wait(50)

# Main game loop
running = True
while running:
    for event in pygame.event.get():
        if event.type == pygame.QUIT:
            running = False
        elif event.type == pygame.MOUSEBUTTONDOWN:
            if event.button == 1:  # Left mouse button
                mouse_pos = pygame.mouse.get_pos()
                if button.is_clicked(mouse_pos):
                    button.clicked = not button.clicked  # Toggle button state

    
    # Fill screen with black
    screen.fill(BLACK)

    # Draw the button
    button.draw(screen)
    
    # Update display
    pygame.display.flip()
    
    # Cap framerate
    clock.tick(60)

# Quit pygame
pygame.quit()
sys.exit()
