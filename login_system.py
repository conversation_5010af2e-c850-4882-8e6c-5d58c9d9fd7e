import json
import os
import hashlib
from datetime import datetime

class LoginSystem:
    def __init__(self):
        self.users_file = "users.json"
        self.notes_file = "user_notes.json"
        self.current_user = None
        self.load_users()
        self.load_notes()
    
    def load_users(self):
        """Load users from file or create empty dict"""
        try:
            with open(self.users_file, 'r') as f:
                self.users = json.load(f)
        except FileNotFoundError:
            self.users = {}
    
    def load_notes(self):
        """Load user notes from file or create empty dict"""
        try:
            with open(self.notes_file, 'r') as f:
                self.user_notes = json.load(f)
        except FileNotFoundError:
            self.user_notes = {}
    
    def save_users(self):
        """Save users to file"""
        with open(self.users_file, 'w') as f:
            json.dump(self.users, f, indent=2)
    
    def save_notes(self):
        """Save user notes to file"""
        with open(self.notes_file, 'w') as f:
            json.dump(self.user_notes, f, indent=2)
    
    def hash_password(self, password):
        """Hash password for security"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def create_account(self, username, password):
        """Create a new user account"""
        if username in self.users:
            return False, "Username already exists!"
        
        if len(password) < 4:
            return False, "Password must be at least 4 characters long!"
        
        # Store hashed password
        self.users[username] = {
            "password": self.hash_password(password),
            "created_date": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        # Initialize empty notes for user
        self.user_notes[username] = []
        
        self.save_users()
        self.save_notes()
        return True, "Account created successfully!"
    
    def login(self, username, password):
        """Login user"""
        if username not in self.users:
            return False, "Username not found!"
        
        if self.users[username]["password"] != self.hash_password(password):
            return False, "Incorrect password!"
        
        self.current_user = username
        return True, f"Welcome back, {username}!"
    
    def logout(self):
        """Logout current user"""
        self.current_user = None
    
    def add_note(self, note):
        """Add a note for current user"""
        if not self.current_user:
            return False, "Please login first!"
        
        note_entry = {
            "note": note,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        
        if self.current_user not in self.user_notes:
            self.user_notes[self.current_user] = []
        
        self.user_notes[self.current_user].append(note_entry)
        self.save_notes()
        return True, "Note saved successfully!"
    
    def get_notes(self):
        """Get all notes for current user"""
        if not self.current_user:
            return []
        
        return self.user_notes.get(self.current_user, [])
    
    def delete_note(self, index):
        """Delete a note by index"""
        if not self.current_user:
            return False, "Please login first!"
        
        notes = self.user_notes.get(self.current_user, [])
        if 0 <= index < len(notes):
            deleted_note = notes.pop(index)
            self.save_notes()
            return True, f"Note deleted: {deleted_note['note'][:30]}..."
        
        return False, "Invalid note index!"

def main():
    system = LoginSystem()
    
    print("=== Personal Login & Notes System ===")
    print("Welcome! You can create an account or login to store your personal notes.")
    
    while True:
        if not system.current_user:
            print("\n--- Main Menu ---")
            print("1. Create Account")
            print("2. Login")
            print("3. Exit")
            
            choice = input("Choose an option (1-3): ").strip()
            
            if choice == "1":
                print("\n--- Create Account ---")
                username = input("Enter username: ").strip()
                password = input("Enter password (min 4 characters): ").strip()
                
                success, message = system.create_account(username, password)
                print(message)
                
            elif choice == "2":
                print("\n--- Login ---")
                username = input("Enter username: ").strip()
                password = input("Enter password: ").strip()
                
                success, message = system.login(username, password)
                print(message)
                
            elif choice == "3":
                print("Goodbye!")
                break
                
            else:
                print("Invalid choice! Please try again.")
        
        else:
            print(f"\n--- Welcome {system.current_user}! ---")
            print("1. Add Note")
            print("2. View All Notes")
            print("3. Delete Note")
            print("4. Logout")
            
            choice = input("Choose an option (1-4): ").strip()
            
            if choice == "1":
                print("\n--- Add Note ---")
                note = input("Enter your note: ").strip()
                if note:
                    success, message = system.add_note(note)
                    print(message)
                else:
                    print("Note cannot be empty!")
                    
            elif choice == "2":
                print("\n--- Your Notes ---")
                notes = system.get_notes()
                if notes:
                    for i, note_entry in enumerate(notes):
                        print(f"{i+1}. [{note_entry['timestamp']}] {note_entry['note']}")
                else:
                    print("No notes found. Add some notes!")
                    
            elif choice == "3":
                print("\n--- Delete Note ---")
                notes = system.get_notes()
                if notes:
                    for i, note_entry in enumerate(notes):
                        print(f"{i+1}. {note_entry['note'][:50]}...")
                    
                    try:
                        index = int(input("Enter note number to delete: ")) - 1
                        success, message = system.delete_note(index)
                        print(message)
                    except ValueError:
                        print("Please enter a valid number!")
                else:
                    print("No notes to delete!")
                    
            elif choice == "4":
                system.logout()
                print("Logged out successfully!")
                
            else:
                print("Invalid choice! Please try again.")

if __name__ == "__main__":
    main()
