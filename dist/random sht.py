import itertools
import string
import time

def brute_force_password(target_password, max_length=10):
    """
    <PERSON><PERSON><PERSON> force a password by trying all combinations
    WARNING: This is for educational purposes only!
    """
    print(f"=== Password Brute Force Tool ===")
    print(f"Target password: {'*' * len(target_password)}")
    print(f"Max length to try: {max_length}")
    print(f"Starting brute force attack...\n")

    # Character set to try (letters and numbers)
    characters = string.ascii_lowercase + string.digits

    attempts = 0
    start_time = time.time()

    # Try passwords of increasing length
    for length in range(1, max_length + 1):
        print(f"Trying passwords of length {length}...")

        # Generate all combinations of the current length
        for combination in itertools.product(characters, repeat=length):
            attempts += 1
            password_attempt = ''.join(combination)

            # Show progress every 1000 attempts
            if attempts % 1000 == 0:
                print(f"Attempts: {attempts}, Current: {password_attempt}")

            # Check if we found the password
            if password_attempt == target_password:
                end_time = time.time()
                time_taken = end_time - start_time

                print(f"\n🎉 PASSWORD CRACKED! 🎉")
                print(f"Password: {password_attempt}")
                print(f"Attempts: {attempts}")
                print(f"Time taken: {time_taken:.2f} seconds")
                return password_attempt

    print(f"\n❌ Password not found within {max_length} characters")
    print(f"Total attempts: {attempts}")
    return None

def dictionary_attack(target_password, dictionary_file=None):
    """
    Try common passwords from a dictionary
    """
    print(f"=== Dictionary Attack ===")

    # Common passwords list
    common_passwords = [
        "password", "123456", "password123", "admin", "qwerty",
        "letmein", "welcome", "monkey", "1234", "pass",
        "test", "user", "login", "guest", "root",
        "abc123", "password1", "123123", "111111", "000000"
    ]

    print(f"Trying {len(common_passwords)} common passwords...")

    for i, password in enumerate(common_passwords, 1):
        print(f"Attempt {i}: {password}")

        if password == target_password:
            print(f"\n🎉 PASSWORD FOUND! 🎉")
            print(f"Password: {password}")
            print(f"Found in {i} attempts using dictionary attack")
            return password

        time.sleep(0.1)  # Small delay to see progress

    print(f"\n❌ Password not found in common passwords list")
    return None

def main():
    print("=== Password Security Demonstration ===")
    print("This tool demonstrates why strong passwords are important!")
    print("\nChoose attack method:")
    print("1. Dictionary Attack (tries common passwords)")
    print("2. Brute Force Attack (tries all combinations)")
    print("3. Set your own target password")
    print("4. Exit")

    while True:
        choice = input("\nEnter choice (1-4): ").strip()

        if choice == "1":
            target = input("Enter target password to crack: ").strip().lower()
            if target:
                result = dictionary_attack(target)
                if not result:
                    print("Try brute force attack for more thorough search!")

        elif choice == "2":
            target = input("Enter target password to crack: ").strip().lower()
            max_len = input("Max password length to try (1-6, default 4): ").strip()

            try:
                max_len = int(max_len) if max_len else 4
                max_len = min(max_len, 6)  # Limit to prevent long waits
            except ValueError:
                max_len = 4

            if target:
                print(f"\n⚠️  WARNING: This may take a long time for passwords longer than 4 characters!")
                confirm = input("Continue? (y/n): ").strip().lower()
                if confirm == 'y':
                    result = brute_force_password(target, max_len)

        elif choice == "3":
            print("\n=== Password Strength Test ===")
            test_password = input("Enter a password to test its strength: ").strip()

            # Test with dictionary attack first
            print(f"\nTesting password: {'*' * len(test_password)}")
            result = dictionary_attack(test_password)

            if not result and len(test_password) <= 4:
                print("\nTrying brute force...")
                result = brute_force_password(test_password, len(test_password))

            if result:
                print(f"\n⚠️  WEAK PASSWORD! Easily cracked.")
                print("Recommendations:")
                print("- Use longer passwords (8+ characters)")
                print("- Mix uppercase, lowercase, numbers, symbols")
                print("- Avoid common words and patterns")
            else:
                print(f"\n✅ STRONG PASSWORD! Not easily cracked.")

        elif choice == "4":
            print("Goodbye! Remember to use strong passwords!")
            break

        else:
            print("Invalid choice! Please try again.")

if __name__ == "__main__":
    main()