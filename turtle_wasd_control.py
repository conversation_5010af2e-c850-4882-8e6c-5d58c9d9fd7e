import turtle
import math

# Set up the screen
screen = turtle.Screen()
screen.title("Turtle WASD Control")
screen.bgcolor("black")
screen.setup(width=800, height=600)
screen.tracer(0)  # Turn off animation for smoother movement

# Create the turtle
player = turtle.Turtle()
player.shape("turtle")
player.color("green")
player.penup()
player.speed(0)

# Movement distance
move_distance = 20

# Trail system
trail_points = []  # Store (x, y) positions
max_trail_distance = 200  # Maximum trail length in pixels
trail_segments = []  # Store trail drawing turtles

# Trail functions
def update_trail():
    """Update the slime trail effect"""
    current_pos = (player.xcor(), player.ycor())
    trail_points.append(current_pos)

    # Remove old trail points that are too far
    total_distance = 0
    points_to_keep = []

    for i in range(len(trail_points) - 1, 0, -1):
        if i < len(trail_points):
            x1, y1 = trail_points[i]
            x2, y2 = trail_points[i-1]
            distance = math.sqrt((x2-x1)**2 + (y2-y1)**2)
            total_distance += distance

            if total_distance <= max_trail_distance:
                points_to_keep.insert(0, trail_points[i-1])
            else:
                break

    points_to_keep.append(current_pos)
    trail_points[:] = points_to_keep

def draw_trail():
    """Draw the fading slime trail"""
    # Clear old trail segments
    for segment in trail_segments:
        segment.clear()

    if len(trail_points) < 2:
        return

    # Calculate total trail distance
    total_distance = 0
    distances = []
    for i in range(1, len(trail_points)):
        x1, y1 = trail_points[i-1]
        x2, y2 = trail_points[i]
        dist = math.sqrt((x2-x1)**2 + (y2-y1)**2)
        distances.append(dist)
        total_distance += dist

    # Draw trail segments with fading effect
    current_distance = 0
    for i in range(len(trail_points) - 1):
        if i >= len(trail_segments):
            # Create new trail segment turtle
            segment = turtle.Turtle()
            segment.hideturtle()
            segment.speed(0)
            trail_segments.append(segment)

        segment = trail_segments[i]
        segment.clear()

        # Calculate fade factor (1.0 = newest, 0.0 = oldest)
        fade_factor = 1.0 - (current_distance / max_trail_distance)
        fade_factor = max(0.1, fade_factor)  # Minimum visibility

        # Set color and width based on fade (REVERSED - thin to thick)
        green_intensity = int(255 * fade_factor)
        trail_width = max(1, int(8 * (1.0 - fade_factor + 0.2)))  # Width from 1 to 8

        segment.color(0, green_intensity/255, 0)  # Green trail
        segment.pensize(trail_width)

        # Draw line segment
        x1, y1 = trail_points[i]
        x2, y2 = trail_points[i + 1]

        segment.penup()
        segment.goto(x1, y1)
        segment.pendown()
        segment.goto(x2, y2)
        segment.penup()

        if i < len(distances):
            current_distance += distances[i]

# Movement functions
def move_up():
    player.setheading(90)  # Point up
    y = player.ycor()
    player.sety(y + move_distance)
    update_trail()

def move_down():
    player.setheading(270)  # Point down
    y = player.ycor()
    player.sety(y - move_distance)
    update_trail()

def move_left():
    player.setheading(180)  # Point left
    x = player.xcor()
    player.setx(x - move_distance)
    update_trail()

def move_right():
    player.setheading(0)  # Point right
    x = player.xcor()
    player.setx(x + move_distance)
    update_trail()

# Bind keys to functions
screen.listen()
screen.onkey(move_up, "w")
screen.onkey(move_down, "s")
screen.onkey(move_left, "a")
screen.onkey(move_right, "d")

# Also bind uppercase keys in case caps lock is on
screen.onkey(move_up, "W")
screen.onkey(move_down, "S")
screen.onkey(move_left, "A")
screen.onkey(move_right, "D")

# Add instructions on screen
instruction_turtle = turtle.Turtle()
instruction_turtle.hideturtle()
instruction_turtle.color("white")
instruction_turtle.penup()
instruction_turtle.goto(-380, 250)
instruction_turtle.write("Use WASD keys to move the turtle!", font=("Arial", 16, "normal"))
instruction_turtle.goto(-380, 220)
instruction_turtle.write("W = Up, S = Down, A = Left, D = Right", font=("Arial", 12, "normal"))

# Initialize trail with starting position
trail_points.append((player.xcor(), player.ycor()))

# Main game loop
while True:
    draw_trail()  # Draw the slime trail
    screen.update()

    # Keep the turtle within screen boundaries
    if player.xcor() > 390:
        player.setx(390)
        update_trail()
    if player.xcor() < -390:
        player.setx(-390)
        update_trail()
    if player.ycor() > 290:
        player.sety(290)
        update_trail()
    if player.ycor() < -290:
        player.sety(-290)
        update_trail()
